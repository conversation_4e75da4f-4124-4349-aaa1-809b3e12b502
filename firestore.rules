rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Users collection - users can only read/write their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Chats collection - users can only read/write chats they own
    match /chats/{chatId} {
      allow read, write: if request.auth != null && request.auth.uid == resource.data.ownerId;
      allow create: if request.auth != null && request.auth.uid == request.resource.data.ownerId;
      
      // Messages subcollection - users can only read/write messages in chats they own
      match /messages/{messageId} {
        allow read, write: if request.auth != null && request.auth.uid == resource.data.chatOwnerId;
        allow create: if request.auth != null && request.auth.uid == request.resource.data.ownerId;
      }
    }
    
    // SystemPersona collection - read-only for authenticated users
    match /systemPersonas/{personaId} {
      allow read: if request.auth != null;
      // Only allow writes from server-side or admin (no client writes)
      allow write: if false;
    }
    
    // Default deny all other access
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
