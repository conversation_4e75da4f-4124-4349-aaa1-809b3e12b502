import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:uuid/uuid.dart';
import '../models/models.dart';

class FirestoreService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const _uuid = Uuid();

  // Get user by userId
  static Future<User?> getUser(String userId) async {
    try {
      final docSnapshot = await _firestore
          .collection('users')
          .doc(userId)
          .get();

      if (docSnapshot.exists && docSnapshot.data() != null) {
        return User.fromJson(docSnapshot.data()!);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get user: $e');
    }
  }

  // Get all chats by ownerId (user uuid)
  static Future<List<Chat>> getChats(String ownerId) async {
    try {
      final querySnapshot = await _firestore
          .collection('chats')
          .where('ownerId', isEqualTo: ownerId)
          .orderBy('lastUpdatedDate', descending: true)
          .get();

      return querySnapshot.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id; // Add the document ID to the data
        return Chat.fromJson(data);
      }).toList();
    } catch (e) {
      throw Exception('Failed to get chats: $e');
    }
  }

  // Get specific chat by ownerId and chatId
  static Future<Chat?> getChat(String ownerId, String chatId) async {
    try {
      final docSnapshot = await _firestore
          .collection('chats')
          .doc(chatId)
          .get();

      if (docSnapshot.exists && docSnapshot.data() != null) {
        final data = docSnapshot.data()!;
        data['id'] = docSnapshot.id; // Add the document ID to the data
        final chat = Chat.fromJson(data);

        // Verify that the chat belongs to the specified owner
        if (chat.ownerId == ownerId) {
          return chat;
        } else {
          throw Exception('Chat does not belong to the specified owner');
        }
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get chat: $e');
    }
  }

  // Create a new chat and persist it to Firestore
  static Future<String> createChat(
    String ownerId, {
    String title = "New Chat",
  }) async {
    try {
      final chatId = _uuid.v4();
      final now = DateTime.now();

      // Create default participants (user + Gemini AI)
      final participants = [
        ChatParticipant(
          personaId: 'gemini-ai',
          name: 'Gemini AI',
          avatarUrl: '', // You can add a default avatar URL here
        ),
      ];

      final chat = Chat(
        title: title,
        ownerId: ownerId,
        startedDate: now,
        lastUpdatedDate: now,
        isCompleted: false,
        participants: participants,
        archived: false,
        metadata: {},
      );

      await _firestore.collection('chats').doc(chatId).set(chat.toJson());

      return chatId;
    } catch (e) {
      throw Exception('Failed to create chat: $e');
    }
  }
}
